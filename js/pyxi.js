// Pyxi Main Application - Consolidated JavaScript
class PyxiApp {
  constructor() {
    this.experiences = {
      dinner: {
        image: './images/reset-table.jpg',
        title: 'The Reset Table',
        desc: 'Intimate dinners designed to spark meaningful conversations'
      },
      couples: {
        image: './images/couples.jpg',
        title: '+1 Nights',
        desc: 'Couples experiences and friend group activities'
      },
      culture: {
        image: './images/culture.jpg',
        title: 'The Culture Edit',
        desc: 'Museums, galleries, and cultural experiences'
      }
    };
    
    this.init();
  }

  init() {
    try {
      this.initScarcityAlert();
      this.initExperienceSwitcher();
      this.initUtils();
      this.initAnimations();
      this.initTracking();
    } catch (error) {
      console.error('PyxiApp initialization failed:', error);
    }
  }

  // Navbar functionality with error handling
  initNavbar() {
    try {
      const navbar = document.getElementById("navbar");
      const mobileMenuButton = document.getElementById("mobileMenuButton");
      const mobileMenu = document.getElementById("mobileMenu");
      const menuIcon = document.getElementById("menuIcon");
      const closeIcon = document.getElementById("closeIcon");

      if (!navbar) {
        console.warn('Navbar element not found');
        return;
      }

      // Debounced scroll handler with consistent height
      let scrollTimeout;
      const handleScroll = () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          try {
            if (window.scrollY > 50) {
              navbar.classList.add('bg-gray-900/95', 'backdrop-blur-sm', 'shadow-lg');
              navbar.classList.remove('bg-transparent');
            } else {
              navbar.classList.remove('bg-gray-900/95', 'backdrop-blur-sm', 'shadow-lg');
              navbar.classList.add('bg-transparent');
            }
          } catch (error) {
            console.error('Scroll handler error:', error);
          }
        }, 16);
      };

      window.addEventListener("scroll", handleScroll, { passive: true });

      // Mobile menu functionality with touch support
      if (mobileMenuButton && mobileMenu && menuIcon && closeIcon) {
        const toggleMenu = (e) => {
          e.preventDefault();
          e.stopPropagation();
          try {
            const isVisible = !mobileMenu.classList.contains("opacity-0");
            this.toggleMobileMenu(isVisible, mobileMenu, menuIcon, closeIcon);
          } catch (error) {
            console.error('Mobile menu toggle error:', error);
          }
        };

        // Add both click and touch events
        mobileMenuButton.addEventListener("click", toggleMenu);
        mobileMenuButton.addEventListener("touchend", toggleMenu);

        // Close mobile menu when clicking on links
        const mobileLinks = mobileMenu.querySelectorAll("a, button");
        mobileLinks.forEach((link) => {
          const closeMenu = (e) => {
            try {
              this.closeMobileMenu(mobileMenu, menuIcon, closeIcon);
            } catch (error) {
              console.error('Mobile menu close error:', error);
            }
          };
          
          link.addEventListener("click", closeMenu);
          link.addEventListener("touchend", closeMenu);
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
          if (!navbar.contains(e.target) && !mobileMenu.classList.contains('opacity-0')) {
            this.closeMobileMenu(mobileMenu, menuIcon, closeIcon);
          }
        });
      }
    } catch (error) {
      console.error('Navbar initialization failed:', error);
    }
  }

  toggleMobileMenu(isVisible, mobileMenu, menuIcon, closeIcon) {
    if (isVisible) {
      mobileMenu.classList.add("opacity-0", "invisible", "-translate-y-full");
      menuIcon.classList.remove("hidden");
      closeIcon.classList.add("hidden");
    } else {
      mobileMenu.classList.remove("opacity-0", "invisible", "-translate-y-full");
      menuIcon.classList.add("hidden");
      closeIcon.classList.remove("hidden");
    }
  }

  closeMobileMenu(mobileMenu, menuIcon, closeIcon) {
    if (mobileMenu) {
      mobileMenu.classList.add('opacity-0', 'invisible', '-translate-y-full');
      if (menuIcon) menuIcon.classList.remove('hidden');
      if (closeIcon) closeIcon.classList.add('hidden');
    }
  }

  // Scarcity alert with real-time countdown
  initScarcityAlert() {
    try {
      const getNextDinner = () => {
        const now = new Date();
        const gmtTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
        const day = gmtTime.getDay();
        const hour = gmtTime.getHours();
        
        let nextDinner = new Date(gmtTime);
        let location;
        
        // Between Thu 7pm and Tue 7pm -> Next dinner Tuesday at Soho
        if ((day === 4 && hour >= 19) || day === 5 || day === 6 || day === 0 || day === 1 || (day === 2 && hour < 19)) {
          // Next Tuesday 7pm
          const daysUntilTuesday = day <= 2 ? (2 - day) : (9 - day);
          nextDinner.setDate(nextDinner.getDate() + daysUntilTuesday);
          nextDinner.setHours(19, 0, 0, 0);
          location = 'Soho';
        } else {
          // Between Tue 7pm and Thu 7pm -> Next dinner Thursday at Canary Wharf
          const daysUntilThursday = day <= 4 ? (4 - day) : (11 - day);
          nextDinner.setDate(nextDinner.getDate() + daysUntilThursday);
          nextDinner.setHours(19, 0, 0, 0);
          location = 'Canary Wharf';
        }
        
        return { nextDinner, location };
      };
      
      const updateCountdown = () => {
        try {
          const { nextDinner, location } = getNextDinner();
          const now = new Date();
          const timeDiff = nextDinner - now;
          
          if (timeDiff > 0) {
            const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
            
            const message = `Next dinner in ${days}d, ${hours}h, ${minutes}m, ${seconds}s at ${location}!`;
            
            const scarcityText1 = document.getElementById('scarcity-text');
            const scarcityText2 = document.getElementById('scarcity-text-2');
            
            if (scarcityText1) scarcityText1.textContent = message;
            if (scarcityText2) scarcityText2.textContent = "⚡ " + message;
          }
        } catch (error) {
          console.error('Countdown update failed:', error);
        }
      };
      
      // Update immediately and then every second
      updateCountdown();
      setInterval(updateCountdown, 1000);
      
    } catch (error) {
      console.error('Scarcity alert initialization failed:', error);
    }
  }

  // Experience switcher functionality
  initExperienceSwitcher() {
    try {
      const experienceItems = document.querySelectorAll('.experience-item');
      const experienceImage = document.getElementById('experience-image');
      const experienceTitle = document.getElementById('experience-title');
      const experienceDesc = document.getElementById('experience-desc');

      if (experienceItems.length > 0 && experienceImage && experienceTitle && experienceDesc) {
        experienceItems.forEach(item => {
          item.addEventListener('mouseenter', () => {
            try {
              const imageKey = item.dataset.image;
              const exp = this.experiences[imageKey];
              if (exp) {
                experienceImage.src = exp.image;
                experienceTitle.textContent = exp.title;
                experienceDesc.textContent = exp.desc;
              }
            } catch (error) {
              console.error('Experience switcher error:', error);
            }
          });
        });
      }
    } catch (error) {
      console.error('Experience switcher initialization failed:', error);
    }
  }

  // Utility functions with error handling
  initUtils() {
    try {
      // Global scroll function
      window.scrollToSection = (sectionId) => {
        try {
          const element = document.getElementById(sectionId);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          } else {
            console.warn(`Section ${sectionId} not found`);
          }
        } catch (error) {
          console.error('Scroll to section failed:', error);
        }
      };

      // Global mobile menu close function
      window.closeMobileMenu = () => {
        try {
          const mobileMenu = document.getElementById('mobileMenu');
          const menuIcon = document.getElementById('menuIcon');
          const closeIcon = document.getElementById('closeIcon');
          this.closeMobileMenu(mobileMenu, menuIcon, closeIcon);
        } catch (error) {
          console.error('Global mobile menu close failed:', error);
        }
      };

      // Error suppression for extensions
      window.addEventListener('error', (e) => {
        if (e.message && e.message.includes('browser is not defined')) {
          e.preventDefault();
          return false;
        }
      });

      // Email validation utility
      window.validateEmail = (email) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
      };
    } catch (error) {
      console.error('Utils initialization failed:', error);
    }
  }

  // Animation and intersection observer
  initAnimations() {
    try {
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('fade-in-up');
          }
        });
      }, observerOptions);

      const animatedElements = document.querySelectorAll('.animate-on-scroll');
      animatedElements.forEach(el => observer.observe(el));
    } catch (error) {
      console.error('Animations initialization failed:', error);
    }
  }

  // Analytics and tracking
  initTracking() {
    try {
      window.trackEvent = (eventName, properties = {}) => {
        console.log('Event tracked:', eventName, properties);
      };

      const ctaButtons = document.querySelectorAll('[data-cta]');
      ctaButtons.forEach(button => {
        button.addEventListener('click', function() {
          try {
            const ctaType = this.dataset.cta;
            window.trackEvent('CTA_Click', { type: ctaType });
          } catch (error) {
            console.error('CTA tracking error:', error);
          }
        });
      });
    } catch (error) {
      console.error('Tracking initialization failed:', error);
    }
  }
}

// Initialize app with multiple fallbacks
const initApp = () => {
  try {
    new PyxiApp();
  } catch (error) {
    console.error('App initialization failed:', error);
  }
};

// Multiple initialization strategies
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

window.addEventListener('load', initApp);