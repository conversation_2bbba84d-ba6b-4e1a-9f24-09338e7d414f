// Simple, clean navbar functionality
document.addEventListener('DOMContentLoaded', function() {
  const navbar = document.getElementById('navbar');
  const mobileMenuBtn = document.getElementById('mobile-menu-btn');
  const mobileMenu = document.getElementById('mobile-menu');
  
  // Scroll effect - transparent to opaque
  let ticking = false;
  function updateNavbar() {
    if (window.scrollY > 50) {
      navbar.classList.add('bg-gray-900/95', 'backdrop-blur-sm');
      // Change logo color to white when navbar has dark background
      const logo = navbar.querySelector('img');
      if (logo) {
        logo.classList.add('filter', 'brightness-0', 'invert');
      }
      // Change hamburger icon to white
      const menuBtn = navbar.querySelector('#mobile-menu-btn');
      if (menuBtn) {
        menuBtn.classList.remove('text-black');
        menuBtn.classList.add('text-white');
      }
    } else {
      navbar.classList.remove('bg-gray-900/95', 'backdrop-blur-sm');
      // Change logo color back to black when navbar is transparent
      const logo = navbar.querySelector('img');
      if (logo) {
        logo.classList.remove('filter', 'brightness-0', 'invert');
      }
      // Change hamburger icon back to black
      const menuBtn = navbar.querySelector('#mobile-menu-btn');
      if (menuBtn) {
        menuBtn.classList.remove('text-white');
        menuBtn.classList.add('text-black');
      }
    }
    ticking = false;
  }
  
  window.addEventListener('scroll', function() {
    if (!ticking) {
      requestAnimationFrame(updateNavbar);
      ticking = true;
    }
  });
  
  // Mobile menu toggle
  if (mobileMenuBtn && mobileMenu) {
    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
    
    // Close mobile menu when clicking links
    const mobileLinks = mobileMenu.querySelectorAll('a, button');
    mobileLinks.forEach(link => {
      link.addEventListener('click', function() {
        mobileMenu.classList.add('hidden');
      });
    });
  }
});

// Global functions
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}

function closeMobileMenu() {
  const mobileMenu = document.getElementById('mobile-menu');
  if (mobileMenu) {
    mobileMenu.classList.add('hidden');
  }
}