/* Custom Focus Styles */
*:focus {
  outline: 2px solid #f09e5b;
  outline-offset: 2px;
}

button:focus,
a:focus,
input:focus,
textarea:focus {
  outline: 2px solid #f09e5b;
  outline-offset: 2px;
}

/* Custom Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Navbar Scroll Effect */
.navbar-scrolled {
  background-color: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(8px);
}

/* Experience Item Hover Effects */
.experience-item {
  transition: all 0.3s ease;
}

.experience-item:hover {
  background-color: rgba(31, 41, 55, 0.5);
  border-color: #f09e5b;
}

/* Location Card Animations */
.location-card {
  transition: all 0.3s ease;
}

.location-card:hover {
  transform: translateY(-4px);
}

/* Button Hover Effects */
.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: scale(1.05);
}

/* Floating Elements */
.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Responsive Video */
.hero-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 768px) {
  .hero-video {
    object-position: center center;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #f09e5b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e07a39;
}