/* Mobile-specific fixes */
@media (max-width: 768px) {
  /* Ensure navbar stays fixed properly on mobile */
  #navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transform: translateZ(0); /* Force hardware acceleration */
  }
  
  /* Prevent text overlap on short screens */
  #about {
    padding-top: 4rem; /* 64px navbar height */
    min-height: calc(100vh - 4rem);
  }
  
  /* Adjust hero content for mobile */
  #about .relative.z-20 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  
  /* Mobile menu improvements */
  #mobileMenu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 40;
  }
}

/* Fix for very short screens */
@media (max-height: 600px) {
  #about {
    padding-top: 4.5rem;
  }
  
  #about h1 {
    font-size: 2.5rem !important;
    line-height: 1.1;
    margin-bottom: 1rem;
  }
  
  #about p {
    font-size: 1rem !important;
  }
}