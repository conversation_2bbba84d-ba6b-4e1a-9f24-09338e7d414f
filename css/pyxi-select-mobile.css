/* Pyxi Select Mobile Responsiveness */
@media (max-width: 1024px) {
  section:has(.experience-item) {
    padding: 4rem 0 !important;
  }
  
  section:has(.experience-item) .grid {
    gap: 2rem !important;
  }
  
  section:has(.experience-item) h2 {
    font-size: 2.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  
  section:has(.experience-item) > div > div > div:first-child > p {
    font-size: 1.125rem !important;
    margin-bottom: 1.5rem !important;
  }
  
  #experience-image {
    height: 20rem !important;
  }
}

@media (max-width: 640px) {
  section:has(.experience-item) {
    padding: 3rem 0 !important;
  }
  
  section:has(.experience-item) h2 {
    font-size: 2rem !important;
    margin-bottom: 1rem !important;
  }
  
  section:has(.experience-item) > div > div > div:first-child > p {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
  }
  
  #experience-image {
    height: 16rem !important;
  }
  
  .experience-item h3 {
    font-size: 1rem !important;
  }
  
  .experience-item p {
    font-size: 0.75rem !important;
  }
}