/* Custom animations and styles */
@keyframes scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.animate-scroll {
  animation: scroll 20s linear infinite;
}

/* Navbar fixes */
#navbar {
  min-height: 80px;
  will-change: background-color, backdrop-filter;
}

#mobileMenu {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .animate-scroll {
    animation: scroll 15s linear infinite;
  }
  
  #navbar {
    min-height: 70px;
  }
}

/* Tablet specific fixes */
@media (min-width: 768px) and (max-width: 1024px) {
  #navbar {
    min-height: 80px;
  }
  
  #mobileMenu {
    display: none !important;
  }
}